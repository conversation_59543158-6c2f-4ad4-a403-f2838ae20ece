


Custom Subaction Event - Projectile/Item Spawn
Creates a new custom subaction event that will spawn new projectiles when triggered. 
Custom Subaction Format (0x10 in length):
F4FFZZAA XXXXYYYY VVVVZZZZ SSSSTTTT

F4 = subaction command byte (always keep as 0xF4)
FF = flag for custom function (always keep as 0xFF)
ZZ = Flags
00 = launch created item
01 = give created item to player
AA = article/projectile/item ID
XXXX = x-offset from player TopN [float]
YYYY = y-offset from player TopN [float]
VVVV = x-velocity [float]
ZZZZ = y-velocity [float]
SSSS = model scale [float]
TTTT = time to expire [float]
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x6E26C ---- 80640008 -> Branch

80A40008 88A50001
2C0500FF 40820170
7C0802A6 90010004
3821FF80 93E1006C
DBC10070 DBE10078
80A40008 90A10068
38C50010 90C40008
7C250B78 38A5FFFC
38C00000 38E00000
94C50004 38E70004
2C070064 40A2FFF4
90610008 9061000C
8063002C 8083002C
90810040 80A10068
88850002 548407FF
41820010 80831974
2C040000 408200DC
88850003 90810010
A0850004 B0810018
C3C10018 C3E3002C
FFDF07B2 C3E300B0
FFDEF82A D3C1001C
D3C10028 A0850006
B0810018 C3C10018
C3E300B4 FFDEF82A
D3C10020 D3C1002C
A0850008 B0810018
C3C10018 C3E3002C
FFDF07B2 D3C10034
A085000A B0810018
C3C10018 D3C10038
3C608026 60638B18
7C6803A6 7C230B78
38630008 4E800021
8063002C 80810068
A0A4000C B0A10018
80A10018 90A30038
88A40002 54A507FF
41820020 3CA08009
60A548A8 7CA803A6
80830004 80630518
4E800021 48000014
A0A4000E B0A10018
80A10018 90A30D44
83E1006C CBC10070
CBE10078 38210080
80010004 7C0803A6
4E800020 7C0802A6
60000000 00000000




	-==-


Modified Subaction Event - Damage Self or Heal Self
Modifies the "Self Damage" event, allowing it to also heal the player.
CC00XXXX = damage self XXXX%
CC01XXXX = heal self XXXX%
[Achilles]
Version -- DOL Offset ------ Hex to Replace ---------- ASM Code
1.02 ------ 0x6F7FC ---- 54843034 -> 60000000

1.02 ------ 0x6F7F8 ---- 8063002C -> 60000000

1.02 ------ 0x6F800 ---- 7C843670 -> 60000000

1.02 ------ 0x6F7F4 ---- 80840000 -> Branch

89C40001 A0840002
8063002C 2C0E0001
40820024 3CA08006
60A5CF5C 7CA903A6
4E800421 3CA08007
60A52C3C 7CA903A6
4E800420 00000000


